/**
 * 虚拟用户配置文件生成器
 * 确保所有指纹信息的一致性
 */

class UserProfileGenerator {
    constructor() {
        this.profiles = this.generateProfiles();
    }

    /**
     * 生成多个一致的用户配置文件
     */
    generateProfiles() {
        const profiles = [];
        
        // Windows 配置文件
        profiles.push({
            os: 'Windows',
            platform: 'Win32',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport: { width: 1920, height: 1080 },
            screen: { width: 1920, height: 1080, colorDepth: 24 },
            hardwareConcurrency: 8,
            deviceMemory: 8,
            languages: ['en-US', 'en'],
            timezone: 'America/New_York',
            webgl: {
                vendor: 'Google Inc. (NVIDIA)',
                renderer: 'ANGLE (NVIDIA, NVIDIA GeForce RTX 3070 Direct3D11 vs_5_0 ps_5_0, D3D11)'
            },
            audio: {
                sampleRate: 48000,
                maxChannelCount: 2,
                numberOfInputs: 1,
                numberOfOutputs: 1
            }
        });

        profiles.push({
            os: 'Windows',
            platform: 'Win32',
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            viewport: { width: 1366, height: 768 },
            screen: { width: 1366, height: 768, colorDepth: 24 },
            hardwareConcurrency: 4,
            deviceMemory: 4,
            languages: ['en-US', 'en'],
            timezone: 'America/Los_Angeles',
            webgl: {
                vendor: 'Google Inc. (Intel)',
                renderer: 'ANGLE (Intel, Intel(R) UHD Graphics 620 Direct3D11 vs_5_0 ps_5_0, D3D11)'
            },
            audio: {
                sampleRate: 44100,
                maxChannelCount: 2,
                numberOfInputs: 1,
                numberOfOutputs: 1
            }
        });

        // macOS 配置文件
        profiles.push({
            os: 'macOS',
            platform: 'MacIntel',
            userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport: { width: 1440, height: 900 },
            screen: { width: 1440, height: 900, colorDepth: 24 },
            hardwareConcurrency: 8,
            deviceMemory: 8,
            languages: ['en-US', 'en'],
            timezone: 'America/New_York',
            webgl: {
                vendor: 'Google Inc. (Apple)',
                renderer: 'ANGLE (Apple, Apple M1 Pro, OpenGL 4.1)'
            },
            audio: {
                sampleRate: 48000,
                maxChannelCount: 2,
                numberOfInputs: 1,
                numberOfOutputs: 1
            }
        });

        return profiles;
    }

    /**
     * 获取随机的一致性配置文件
     */
    getRandomProfile() {
        const profile = this.profiles[Math.floor(Math.random() * this.profiles.length)];
        
        // 添加一些随机变化但保持一致性
        const variation = {
            ...profile,
            // 轻微的屏幕分辨率变化
            screen: {
                ...profile.screen,
                width: profile.screen.width + Math.floor(Math.random() * 20) - 10,
                height: profile.screen.height + Math.floor(Math.random() * 20) - 10
            },
            // 轻微的硬件变化
            hardwareConcurrency: profile.hardwareConcurrency + (Math.random() > 0.5 ? 0 : Math.random() > 0.5 ? 2 : -2),
            // 随机的设备内存（但符合常见配置）
            deviceMemory: [4, 8, 16][Math.floor(Math.random() * 3)]
        };

        // 确保硬件并发数合理
        variation.hardwareConcurrency = Math.max(2, Math.min(16, variation.hardwareConcurrency));

        return variation;
    }

    /**
     * 生成一致的字体列表
     */
    getFontList(profile) {
        const commonFonts = [
            'Arial', 'Helvetica', 'Times New Roman', 'Courier New', 'Verdana',
            'Georgia', 'Palatino', 'Garamond', 'Bookman', 'Comic Sans MS',
            'Trebuchet MS', 'Arial Black', 'Impact'
        ];

        const windowsFonts = [
            'Segoe UI', 'Tahoma', 'Microsoft Sans Serif', 'Calibri', 'Cambria',
            'Consolas', 'Lucida Console', 'MS Gothic', 'MS Mincho'
        ];

        const macFonts = [
            'San Francisco', 'Helvetica Neue', 'Lucida Grande', 'Monaco',
            'Menlo', 'Avenir', 'Optima', 'Futura', 'Gill Sans'
        ];

        let fonts = [...commonFonts];
        
        if (profile.os === 'Windows') {
            fonts = fonts.concat(windowsFonts);
        } else if (profile.os === 'macOS') {
            fonts = fonts.concat(macFonts);
        }

        return fonts;
    }

    /**
     * 生成一致的插件列表
     */
    getPluginList(profile) {
        const basePlugins = [
            {
                name: 'PDF Viewer',
                filename: 'internal-pdf-viewer',
                description: 'Portable Document Format'
            },
            {
                name: 'Chrome PDF Plugin',
                filename: 'internal-pdf-plugin',
                description: 'Portable Document Format'
            }
        ];

        if (profile.os === 'Windows') {
            basePlugins.push({
                name: 'Microsoft Edge PDF Plugin',
                filename: 'edge-pdf-plugin',
                description: 'Portable Document Format'
            });
        }

        return basePlugins;
    }

    /**
     * 生成WebRTC配置
     */
    getWebRTCConfig(profile) {
        const configs = {
            Windows: {
                localIP: `192.168.1.${Math.floor(Math.random() * 200) + 10}`,
                publicIP: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
                stunServers: ['stun:stun.l.google.com:19302']
            },
            macOS: {
                localIP: `192.168.1.${Math.floor(Math.random() * 200) + 10}`,
                publicIP: `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
                stunServers: ['stun:stun.l.google.com:19302']
            }
        };

        return configs[profile.os] || configs.Windows;
    }

    /**
     * 生成电池信息
     */
    getBatteryInfo(profile) {
        // 桌面设备通常没有电池或者是充电状态
        return {
            charging: true,
            chargingTime: 0,
            dischargingTime: Infinity,
            level: 1.0
        };
    }
}

module.exports = UserProfileGenerator;
