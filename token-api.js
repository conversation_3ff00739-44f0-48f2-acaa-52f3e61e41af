const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.TOKEN_API_PORT || 9043;
const AUTH_PASSWORD = process.env.AUTH_PASSWORD;
const TOKENS_FILE = path.join(__dirname, 'tokens.json');

// Middleware
app.use(cors());
app.use(express.json());

// Authentication middleware
function authenticateToken(req, res, next) {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token || token !== AUTH_PASSWORD) {
    return res.status(401).json({
      success: false,
      error: 'Unauthorized',
      message: 'Invalid authentication token'
    });
  }

  next();
}

// Helper function to read tokens from file
function readTokens() {
  try {
    const data = fs.readFileSync(TOKENS_FILE, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error('Error reading tokens file:', error);
    return [];
  }
}

// Helper function to write tokens to file
function writeTokens(tokens) {
  try {
    fs.writeFileSync(TOKENS_FILE, JSON.stringify(tokens, null, 2), 'utf8');
    return true;
  } catch (error) {
    console.error('Error writing tokens file:', error);
    return false;
  }
}

// Helper function to find and mark an unused token
function getAndMarkUnusedToken() {
  const tokens = readTokens();

  // Find first unused token (no 'used' field or used: false)
  const unusedTokenIndex = tokens.findIndex(token => !token.used);

  if (unusedTokenIndex === -1) {
    return null; // No unused tokens found
  }

  // Mark token as used
  tokens[unusedTokenIndex].used = true;

  // Save back to file
  if (!writeTokens(tokens)) {
    return null; // Failed to save
  }

  // Return the token in API format
  const token = tokens[unusedTokenIndex];
  return {
    id: token.id,
    accessToken: token.access_token,
    tenantURL: token.tenant_url,
    description: token.description || 'Token from tokens.json',
    createdAt: token.createdTime || token.createdTimestamp ? new Date(token.createdTimestamp || token.createdTime).toISOString() : new Date().toISOString()
  };
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'Token API is running',
    timestamp: new Date().toISOString()
  });
});

// Main API endpoint - Get one unused token
app.get('/api/tokens', authenticateToken, (req, res) => {
  try {
    const token = getAndMarkUnusedToken();

    if (!token) {
      return res.status(404).json({
        success: false,
        error: 'No available tokens',
        message: 'All tokens have been used or no tokens found',
        timestamp: new Date().toISOString()
      });
    }

    res.json({
      success: true,
      token: token,
      timestamp: new Date().toISOString()
    });

    console.log(`Token ${token.id} has been returned and marked as used`);

  } catch (error) {
    console.error('Error processing token request:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to process token request',
      timestamp: new Date().toISOString()
    });
  }
});

// Get token statistics (for debugging)
app.get('/api/tokens/stats', authenticateToken, (req, res) => {
  try {
    const tokens = readTokens();
    const usedCount = tokens.filter(token => token.used).length;
    const unusedCount = tokens.length - usedCount;

    res.json({
      success: true,
      stats: {
        total: tokens.length,
        used: usedCount,
        unused: unusedCount
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error getting token stats:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      message: 'Failed to get token statistics'
    });
  }
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    error: 'Not Found',
    message: 'Endpoint not found'
  });
});

// Error handler
app.use((error, req, res, next) => {
  console.error('Unhandled error:', error);
  res.status(500).json({
    success: false,
    error: 'Internal server error',
    message: 'An unexpected error occurred'
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`Token API server is running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
  console.log(`API endpoint: http://localhost:${PORT}/api/tokens`);
  console.log(`Authentication: Bearer ${AUTH_PASSWORD ? '[CONFIGURED]' : '[NOT CONFIGURED]'}`);

  // Check if tokens file exists
  if (fs.existsSync(TOKENS_FILE)) {
    const tokens = readTokens();
    const unusedCount = tokens.filter(token => !token.used).length;
    console.log(`Found ${tokens.length} total tokens, ${unusedCount} unused`);
  } else {
    console.warn(`Warning: tokens.json file not found at ${TOKENS_FILE}`);
  }
});

module.exports = app;
