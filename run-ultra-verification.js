/**
 * 超级增强版邮箱验证脚本
 * 使用最新的抗指纹检测技术和智能行为模拟
 */

const puppeteer = require('puppeteer');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const puppeteerExtra = require('puppeteer-extra');
const fs = require('fs');
const path = require('path');

const AutoRegister = require('./index.js');
const AugmentAuth = require('./augment-auth.js');
const TokenStorage = require('./token-storage.js');
const EnhancedAntiFingerprint = require('./enhanced-anti-fingerprint.js');
const NetworkDisguise = require('./network-disguise.js');

require('dotenv').config();

// 使用stealth插件
puppeteerExtra.use(StealthPlugin());

class UltraAutoRegister extends AutoRegister {
    constructor() {
        super();
        this.enhancedAntiFingerprint = new EnhancedAntiFingerprint();
        this.networkDisguise = new NetworkDisguise();
        this.sessionId = `ultra_session_${Date.now()}`;
    }

    /**
     * 重写浏览器初始化方法
     */
    async initBrowser() {
        try {
            this.log('🚀 启动超级增强版浏览器...');

            // 使用超级增强的启动参数
            const launchOptions = this.enhancedAntiFingerprint.getUltraLaunchOptions();

            this.browser = await puppeteerExtra.launch(launchOptions);
            this.page = await this.browser.newPage();
            this.page.setDefaultTimeout(120000);
            this.page.setDefaultNavigationTimeout(120000);

            // 设置网络层伪装
            await this.networkDisguise.setupNetworkDisguise(this.page);

            // 模拟DNS预解析和资源预加载
            await this.networkDisguise.simulateDNSPrefetch(this.page);
            await this.networkDisguise.simulateResourcePreload(this.page);

            // 应用超级增强的抗指纹检测
            await this.enhancedAntiFingerprint.applyUltraAntiFingerprinting(this.page);

            // 显示当前配置文件信息
            const profile = this.enhancedAntiFingerprint.getCurrentProfile();
            this.log(`🎭 用户配置: ${profile.os} | ${profile.viewport.width}x${profile.viewport.height} | ${profile.hardwareConcurrency}核`);

            this.log('✅ 超级增强版浏览器启动完成');
            return this.page;
        } catch (error) {
            this.log(`❌ 浏览器启动失败: ${error.message}`);
            throw error;
        }
    }

    /**
     * 重写等待方法，使用智能等待
     */
    async wait(ms) {
        await this.enhancedAntiFingerprint.smartWait(this.page, ms);
    }

    /**
     * 重写输入方法，使用智能输入
     */
    async typeText(selector, text) {
        this.log(`📝 智能输入: ${text}`);

        // 先模拟一下犹豫
        if (Math.random() < 0.3) {
            await this.enhancedAntiFingerprint.simulateHesitation(this.page);
        }

        const success = await this.enhancedAntiFingerprint.smartType(this.page, selector, text);
        if (!success) {
            throw new Error(`无法在 ${selector} 中输入文本`);
        }

        // 输入后短暂停顿
        await this.enhancedAntiFingerprint.smartWait(this.page, 500);
        return success;
    }

    /**
     * 重写点击方法，使用智能点击
     */
    async clickElement(selector) {
        this.log(`🖱️ 智能点击: ${selector}`);

        // 点击前可能的阅读行为
        if (Math.random() < 0.4) {
            await this.enhancedAntiFingerprint.simulateReading(this.page, 1000);
        }

        const success = await this.enhancedAntiFingerprint.smartClick(this.page, selector);
        if (!success) {
            throw new Error(`无法点击元素: ${selector}`);
        }

        // 点击后等待
        await this.enhancedAntiFingerprint.smartWait(this.page, 800);
        return success;
    }

    /**
     * 增强的页面导航
     */
    async navigateToPage(url) {
        this.log(`🌐 导航到: ${url}`);

        // 导航前的随机延迟
        await this.enhancedAntiFingerprint.smartWait(this.page, 1000);

        await this.page.goto(url, {
            waitUntil: 'networkidle2',
            timeout: 120000
        });

        // 页面加载后模拟阅读
        await this.enhancedAntiFingerprint.simulateReading(this.page, 2000);

        await this.takeScreenshot('page_loaded');
        await this.savePageHTML('page_loaded');
    }

    /**
     * 增强的邮箱验证流程
     */
    async handleEmailVerificationWithOneMailAPI(authUrl) {
        try {
            this.log('🚀 开始超级增强版邮箱验证流程');

            await this.initBrowser();

            // 导航到授权页面
            await this.navigateToPage(authUrl);

            // 模拟用户查看页面
            await this.enhancedAntiFingerprint.simulateReading(this.page, 3000);

            // 查找并点击登录按钮
            const signInSelectors = [
                'button[type="submit"]',
                'input[type="submit"]',
                'button:contains("Sign in")',
                'a:contains("Sign in")',
                '.sign-in-button',
                '#sign-in-button'
            ];

            let signInButton = null;
            for (const selector of signInSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    signInButton = await this.page.$(selector);
                    if (signInButton) break;
                } catch (e) { continue; }
            }

            if (!signInButton) {
                throw new Error('未找到登录按钮');
            }

            // 智能点击登录按钮
            await this.clickElement(signInSelectors.find(s => signInButton));
            await this.takeScreenshot('sign_in_clicked');

            // 处理验证码（如果存在）
            await this.handleCaptchaIfPresent();

            // 生成临时邮箱
            this.log('📧 生成临时邮箱...');
            const emailData = await this.oneMailHandler.generateTempEmail();
            this.tempEmail = emailData.email;
            this.log(`📧 临时邮箱: ${this.tempEmail}`);

            // 查找邮箱输入框并输入
            const emailSelectors = [
                'input[type="email"]',
                'input[name="email"]',
                'input[placeholder*="email"]',
                '#email',
                '.email-input'
            ];

            let emailInput = null;
            for (const selector of emailSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 5000 });
                    emailInput = await this.page.$(selector);
                    if (emailInput) {
                        await this.typeText(selector, this.tempEmail);
                        break;
                    }
                } catch (e) { continue; }
            }

            if (!emailInput) {
                throw new Error('未找到邮箱输入框');
            }

            await this.takeScreenshot('email_entered');

            // 查找并点击继续按钮
            const continueSelectors = [
                'button:contains("Continue")',
                'button:contains("Next")',
                'button[type="submit"]',
                'input[type="submit"]',
                '.continue-button',
                '#continue-button'
            ];

            for (const selector of continueSelectors) {
                try {
                    const button = await this.page.$(selector);
                    if (button) {
                        await this.clickElement(selector);
                        break;
                    }
                } catch (e) { continue; }
            }

            await this.takeScreenshot('continue_clicked');

            // 等待验证码邮件
            this.log('⏳ 等待验证码邮件...');
            const verificationCode = await this.oneMailHandler.waitForVerificationCode(this.tempEmail);
            this.log(`🔑 收到验证码: ${verificationCode}`);

            // 查找验证码输入框并输入
            const codeSelectors = [
                'input[name="code"]',
                'input[placeholder*="code"]',
                'input[type="text"]',
                '#verification-code',
                '.verification-input'
            ];

            for (const selector of codeSelectors) {
                try {
                    await this.page.waitForSelector(selector, { timeout: 10000 });
                    const codeInput = await this.page.$(selector);
                    if (codeInput) {
                        await this.typeText(selector, verificationCode);
                        break;
                    }
                } catch (e) { continue; }
            }

            await this.takeScreenshot('code_entered');

            // 点击最终的继续按钮
            for (const selector of continueSelectors) {
                try {
                    const button = await this.page.$(selector);
                    if (button) {
                        await this.clickElement(selector);
                        break;
                    }
                } catch (e) { continue; }
            }

            await this.takeScreenshot('final_continue_clicked');

            // 等待验证完成
            await this.page.waitForTimeout(5000);
            await this.takeScreenshot('verification_complete');
            await this.savePageHTML('verification_complete');

            // 检查是否成功
            const pageContent = await this.page.content();
            if (pageContent.includes('Sign-up rejected') || pageContent.includes('rejected')) {
                throw new Error('注册被拒绝 - 可能被反欺诈系统检测');
            }

            // 尝试获取授权码
            const clipboardContent = await this.getClipboardContent();

            // 显示行为统计
            const stats = this.enhancedAntiFingerprint.getBehaviorStats();
            this.log('📊 行为统计:');
            this.log(`   鼠标移动: ${stats.mouseMovements} 次`);
            this.log(`   打字事件: ${stats.typingEvents} 次`);
            this.log(`   会话时长: ${Math.round(stats.sessionDuration / 1000)} 秒`);
            this.log(`   平均打字速度: ${Math.round(stats.averageTypingSpeed)} ms/字符`);

            return clipboardContent;

        } catch (error) {
            this.log(`❌ 超级增强版验证失败: ${error.message}`);
            await this.takeScreenshot('ultra_verification_error');
            await this.savePageHTML('ultra_verification_error');
            throw error;
        } finally {
            if (this.browser) {
                await this.browser.close();
            }
        }
    }
}

async function runUltraEmailVerification() {
    const ultraAutoRegister = new UltraAutoRegister();
    const augmentAuth = new AugmentAuth();
    const tokenStorage = new TokenStorage();

    try {
        console.log('🚀 开始超级增强版 Augment 授权和邮箱验证流程');
        console.log('🎭 使用最新的抗指纹检测技术和智能行为模拟');
        console.log('');

        // 步骤1: 生成 Augment 授权 URL
        console.log('🔐 步骤1: 生成 Augment 授权 URL');
        const authUrl = augmentAuth.generateAuthUrl();
        process.env.LINK_TO_TEST = authUrl;

        console.log('✅ 授权 URL 已生成并设置');
        console.log(`📧 目标URL: ${authUrl}`);
        console.log('🎭 使用虚拟用户配置文件和智能行为模拟');
        console.log('🧠 包含贝塞尔曲线鼠标移动、真实打字节奏、随机错误等');
        console.log('');

        // 步骤2: 执行超级增强版邮箱验证流程
        console.log('🔐 步骤2: 执行超级增强版邮箱验证和授权流程');
        const clipboardContent = await ultraAutoRegister.handleEmailVerificationWithOneMailAPI(authUrl);

        if (clipboardContent) {
            console.log('');
            console.log('✅ 超级增强版验证成功！获取到授权码！');
            console.log(`📋 授权码: ${clipboardContent.substring(0, 50)}...`);

            try {
                // 完成真实的 OAuth 流程
                console.log('🚀 开始调用真实的 Augment API...');
                const tokenResponse = await augmentAuth.completeOAuthFlow(clipboardContent);

                console.log('');
                console.log('✅ 真实 API 调用成功！获取到真实访问令牌！');
                console.log(`🔑 真实访问令牌: ${tokenResponse.access_token?.substring(0, 30)}...`);
                console.log(`🏢 租户 URL: ${tokenResponse.tenant_url}`);

                // 保存真实令牌
                const tokenId = tokenStorage.addToken(tokenResponse, {
                    description: 'Ultra enhanced token from Augment API',
                    user_agent: 'augment-ultra-verification',
                    session_id: ultraAutoRegister.sessionId,
                    anti_fingerprint_version: 'ultra-v2.0'
                });

                console.log('');
                console.log('🎉 超级增强版流程成功完成！');
                console.log(`💾 真实令牌已保存，ID: ${tokenId}`);
                console.log('📊 令牌统计:');
                tokenStorage.getStats();

            } catch (tokenError) {
                console.error('');
                console.error('❌ 真实 API 调用失败:', tokenError.message);
                console.error('📋 剪贴板内容已保存，可手动处理');
            }
        }

        console.log('');
        console.log('🎉 超级增强版邮箱验证流程完成！');
        console.log('📁 请查看 image/ 目录中的截图和HTML文件');

    } catch (error) {
        console.error('');
        console.error('💥 超级增强版验证流程失败:', error.message);
        console.error('📁 请查看 image/ 目录中的错误截图进行调试');
        process.exit(1);
    }
}

if (require.main === module) {
    runUltraEmailVerification();
}

module.exports = { UltraAutoRegister, runUltraEmailVerification };
