/**
 * 智能行为模拟器
 * 模拟真实用户的行为模式
 */

class BehaviorSimulator {
    constructor() {
        this.mouseHistory = [];
        this.typingHistory = [];
        this.sessionStartTime = Date.now();
    }

    /**
     * 生成贝塞尔曲线鼠标移动路径
     */
    generateMousePath(startX, startY, endX, endY, steps = 20) {
        const path = [];

        // 生成控制点，添加一些随机性
        const cp1x = startX + (endX - startX) * 0.25 + (Math.random() - 0.5) * 100;
        const cp1y = startY + (endY - startY) * 0.25 + (Math.random() - 0.5) * 100;
        const cp2x = startX + (endX - startX) * 0.75 + (Math.random() - 0.5) * 100;
        const cp2y = startY + (endY - startY) * 0.75 + (Math.random() - 0.5) * 100;

        for (let i = 0; i <= steps; i++) {
            const t = i / steps;
            const x = Math.pow(1 - t, 3) * startX +
                3 * Math.pow(1 - t, 2) * t * cp1x +
                3 * (1 - t) * Math.pow(t, 2) * cp2x +
                Math.pow(t, 3) * endX;
            const y = Math.pow(1 - t, 3) * startY +
                3 * Math.pow(1 - t, 2) * t * cp1y +
                3 * (1 - t) * Math.pow(t, 2) * cp2y +
                Math.pow(t, 3) * endY;

            path.push({ x: Math.round(x), y: Math.round(y) });
        }

        return path;
    }

    /**
     * 智能鼠标移动
     */
    async smartMouseMove(page, targetX, targetY) {
        const currentMouse = await page.evaluate(() => ({
            x: window.mouseX || Math.random() * window.innerWidth,
            y: window.mouseY || Math.random() * window.innerHeight
        }));

        const path = this.generateMousePath(
            currentMouse.x, currentMouse.y,
            targetX, targetY
        );

        // 模拟真实的鼠标移动速度
        const baseDelay = 10;
        const randomDelay = () => baseDelay + Math.random() * 20;

        for (let i = 0; i < path.length; i++) {
            const point = path[i];
            await page.mouse.move(point.x, point.y);

            // 记录鼠标位置
            await page.evaluate((x, y) => {
                window.mouseX = x;
                window.mouseY = y;
            }, point.x, point.y);

            if (i < path.length - 1) {
                await page.waitForTimeout(randomDelay());
            }
        }

        this.mouseHistory.push({ x: targetX, y: targetY, timestamp: Date.now() });
    }

    /**
     * 智能点击（包含随机的小错误）
     */
    async smartClick(page, selector) {
        const element = await page.$(selector);
        if (!element) return false;

        const box = await element.boundingBox();
        if (!box) return false;

        // 10% 概率先点击错误位置（模拟真实用户的误操作）
        if (Math.random() < 0.1) {
            const wrongX = box.x + box.width * (Math.random() * 0.3 + 0.1);
            const wrongY = box.y + box.height * (Math.random() * 0.3 + 0.1);

            await this.smartMouseMove(page, wrongX, wrongY);
            await page.waitForTimeout(100 + Math.random() * 200);

            // 然后"纠正"到正确位置
            await page.waitForTimeout(200 + Math.random() * 300);
        }

        // 点击目标位置（在元素中心附近随机）
        const targetX = box.x + box.width * (0.3 + Math.random() * 0.4);
        const targetY = box.y + box.height * (0.3 + Math.random() * 0.4);

        await this.smartMouseMove(page, targetX, targetY);

        // 模拟真实的点击延迟
        await page.waitForTimeout(50 + Math.random() * 100);
        await page.mouse.click(targetX, targetY, {
            delay: 50 + Math.random() * 100
        });

        return true;
    }

    /**
     * 智能打字（模拟真实的打字节奏）
     */
    async smartType(page, selector, text) {
        const element = await page.$(selector);
        if (!element) return false;

        // 先点击输入框
        await this.smartClick(page, selector);
        await page.waitForTimeout(100 + Math.random() * 200);

        // 清空现有内容
        await page.keyboard.down('Control');
        await page.keyboard.press('KeyA');
        await page.keyboard.up('Control');
        await page.waitForTimeout(50);

        // 模拟真实的打字节奏
        for (let i = 0; i < text.length; i++) {
            const char = text[i];

            // 计算打字延迟（基于字符类型和位置）
            let delay = this.calculateTypingDelay(char, i, text);

            // 5% 概率出现"打字错误"然后纠正
            if (Math.random() < 0.05 && i > 0) {
                // 打错一个字符
                const wrongChar = String.fromCharCode(char.charCodeAt(0) + (Math.random() > 0.5 ? 1 : -1));
                await page.keyboard.type(wrongChar, { delay: delay });
                await page.waitForTimeout(100 + Math.random() * 200);

                // 删除错误字符
                await page.keyboard.press('Backspace');
                await page.waitForTimeout(50 + Math.random() * 100);

                // 重新打正确字符
                delay = this.calculateTypingDelay(char, i, text);
            }

            await page.keyboard.type(char, { delay });

            // 记录打字历史
            this.typingHistory.push({
                char,
                delay,
                timestamp: Date.now()
            });
        }

        return true;
    }

    /**
     * 计算真实的打字延迟
     */
    calculateTypingDelay(char, position, fullText) {
        let baseDelay = 120; // 基础延迟

        // 根据字符类型调整
        if (char === ' ') {
            baseDelay = 80; // 空格打得快一些
        } else if (/[A-Z]/.test(char)) {
            baseDelay = 150; // 大写字母慢一些
        } else if (/[0-9]/.test(char)) {
            baseDelay = 140; // 数字稍慢
        } else if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(char)) {
            baseDelay = 180; // 特殊字符最慢
        }

        // 根据位置调整（开始打字时慢一些）
        if (position < 3) {
            baseDelay *= 1.3;
        }

        // 添加随机变化（正态分布）
        const randomFactor = this.normalRandom(0.8, 1.4);
        baseDelay *= randomFactor;

        // 模拟疲劳效应（打字越久越慢）
        const fatigueMultiplier = 1 + (position / fullText.length) * 0.2;
        baseDelay *= fatigueMultiplier;

        return Math.max(50, Math.round(baseDelay));
    }

    /**
     * 生成正态分布随机数
     */
    normalRandom(min, max) {
        let u = 0, v = 0;
        while (u === 0) u = Math.random();
        while (v === 0) v = Math.random();

        const normal = Math.sqrt(-2.0 * Math.log(u)) * Math.cos(2.0 * Math.PI * v);
        const scaled = normal * 0.1 + 1;

        return Math.max(min, Math.min(max, scaled));
    }

    /**
     * 模拟阅读行为
     */
    async simulateReading(page, duration = 2000) {
        const readingTime = duration + Math.random() * duration;
        const steps = Math.floor(readingTime / 200);

        for (let i = 0; i < steps; i++) {
            // 随机滚动一点
            if (Math.random() < 0.3) {
                await page.evaluate(() => {
                    window.scrollBy(0, Math.random() * 100 - 50);
                });
            }

            // 随机移动鼠标（模拟眼动追踪）
            if (Math.random() < 0.4) {
                const randomX = Math.random() * 800 + 200;
                const randomY = Math.random() * 400 + 200;
                await this.smartMouseMove(page, randomX, randomY);
            }

            await page.waitForTimeout(200 + Math.random() * 300);
        }
    }

    /**
     * 模拟思考时间
     */
    async simulateThinking(baseTime = 1000) {
        const thinkingTime = baseTime * this.normalRandom(0.5, 2.0);
        await new Promise(resolve => setTimeout(resolve, thinkingTime));
    }

    /**
     * 随机页面交互
     */
    async randomPageInteraction(page) {
        const actions = [
            // 随机滚动
            async () => {
                const scrollAmount = Math.random() * 300 - 150;
                await page.evaluate((amount) => {
                    window.scrollBy(0, amount);
                }, scrollAmount);
            },
            // 随机鼠标移动
            async () => {
                const x = Math.random() * 1000 + 100;
                const y = Math.random() * 600 + 100;
                await this.smartMouseMove(page, x, y);
            },
            // 随机悬停
            async () => {
                const elements = await page.$$('button, a, input');
                if (elements.length > 0) {
                    const randomElement = elements[Math.floor(Math.random() * elements.length)];
                    const box = await randomElement.boundingBox();
                    if (box) {
                        await this.smartMouseMove(page,
                            box.x + box.width / 2,
                            box.y + box.height / 2
                        );
                        await page.waitForTimeout(500 + Math.random() * 1000);
                    }
                }
            }
        ];

        // 30% 概率执行随机交互
        if (Math.random() < 0.3) {
            const randomAction = actions[Math.floor(Math.random() * actions.length)];
            await randomAction();
        }
    }

    /**
     * 获取行为统计
     */
    getBehaviorStats() {
        return {
            mouseMovements: this.mouseHistory.length,
            typingEvents: this.typingHistory.length,
            sessionDuration: Date.now() - this.sessionStartTime,
            averageTypingSpeed: this.calculateAverageTypingSpeed()
        };
    }

    /**
     * 计算平均打字速度
     */
    calculateAverageTypingSpeed() {
        if (this.typingHistory.length < 2) return 0;

        const totalDelay = this.typingHistory.reduce((sum, event) => sum + event.delay, 0);
        return totalDelay / this.typingHistory.length;
    }

    /**
     * 模拟用户犹豫行为
     */
    async simulateHesitation(page) {
        // 随机停顿，模拟用户思考
        await page.waitForTimeout(1000 + Math.random() * 3000);

        // 可能的鼠标移动（表示犹豫）
        if (Math.random() < 0.6) {
            const currentPos = await page.evaluate(() => ({
                x: window.mouseX || window.innerWidth / 2,
                y: window.mouseY || window.innerHeight / 2
            }));

            // 小范围随机移动
            for (let i = 0; i < 3; i++) {
                const newX = currentPos.x + (Math.random() - 0.5) * 100;
                const newY = currentPos.y + (Math.random() - 0.5) * 100;
                await this.smartMouseMove(page, newX, newY);
                await page.waitForTimeout(200 + Math.random() * 500);
            }
        }
    }
}

module.exports = BehaviorSimulator;
