const crypto = require('crypto');

/**
 * 生成OAuth 2.0 PKCE授权链接
 * 
 * OAuth 2.0 PKCE (Proof Key for Code Exchange) 流程说明：
 * 1. 客户端生成一个随机的 code_verifier
 * 2. 对 code_verifier 进行 SHA256 哈希并 base64url 编码得到 code_challenge
 * 3. 将用户重定向到授权服务器，携带 code_challenge
 * 4. 用户授权后，授权服务器返回 authorization_code
 * 5. 客户端使用 authorization_code 和原始的 code_verifier 换取 access_token
 */

/**
 * 生成随机字符串
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
function generateRandomString(length) {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~';
    let result = '';
    const randomBytes = crypto.randomBytes(length);
    
    for (let i = 0; i < length; i++) {
        result += charset[randomBytes[i] % charset.length];
    }
    
    return result;
}

/**
 * Base64URL 编码
 * @param {Buffer} buffer - 要编码的数据
 * @returns {string} Base64URL编码的字符串
 */
function base64URLEncode(buffer) {
    return buffer
        .toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}

/**
 * 生成 PKCE code challenge
 * @param {string} codeVerifier - code verifier
 * @returns {string} code challenge
 */
function generateCodeChallenge(codeVerifier) {
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    return base64URLEncode(hash);
}

/**
 * 生成OAuth授权链接
 * @returns {object} 包含授权链接和相关参数的对象
 */
function generateOAuthLink() {
    // 1. 生成 code_verifier (43-128个字符)
    const codeVerifier = generateRandomString(64);
    
    // 2. 生成 code_challenge
    const codeChallenge = generateCodeChallenge(codeVerifier);
    
    // 3. 生成随机 state (防止CSRF攻击)
    const state = generateRandomString(10);
    
    // 4. 构建授权URL的参数
    const authParams = {
        response_type: 'code',           // 使用授权码流程
        code_challenge: codeChallenge,   // PKCE challenge
        client_id: 'v',                  // 客户端ID
        state: state,                    // 防CSRF状态值
        prompt: 'login'                  // 强制显示登录页面
    };
    
    // 5. 构建完整的授权URL
    const baseUrl = 'https://auth.augmentcode.com/authorize';
    const queryString = Object.entries(authParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');
    
    const authUrl = `${baseUrl}?${queryString}`;
    
    return {
        authUrl,
        codeVerifier,  // 需要保存，后续换取token时使用
        codeChallenge,
        state,
        authParams
    };
}

/**
 * 主函数 - 生成并打印OAuth授权链接
 */
function main() {
    console.log('🔐 OAuth 2.0 PKCE 授权链接生成器');
    console.log('=' .repeat(50));
    
    const result = generateOAuthLink();
    
    console.log('\n📋 生成的授权链接:');
    console.log(result.authUrl);
    
    console.log('\n🔧 生成参数详情:');
    console.log(`Code Verifier: ${result.codeVerifier}`);
    console.log(`Code Challenge: ${result.codeChallenge}`);
    console.log(`State: ${result.state}`);
    
    console.log('\n📖 参数说明:');
    console.log('• response_type=code: 使用OAuth 2.0授权码流程');
    console.log('• code_challenge: PKCE安全机制，防止授权码拦截攻击');
    console.log('• client_id=v: 应用的客户端标识符');
    console.log('• state: 随机状态值，防止CSRF攻击');
    console.log('• prompt=login: 强制用户重新登录');
    
    console.log('\n🔄 PKCE流程说明:');
    console.log('1. 生成随机的 code_verifier');
    console.log('2. 对 code_verifier 进行 SHA256 + Base64URL 编码得到 code_challenge');
    console.log('3. 用户访问授权链接进行授权');
    console.log('4. 授权服务器返回 authorization_code');
    console.log('5. 使用 authorization_code + code_verifier 换取 access_token');
    
    console.log('\n⚠️  重要提醒:');
    console.log('请保存 code_verifier，在后续换取 access_token 时需要使用！');
    
    return result;
}

// 如果直接运行此脚本，则执行主函数
if (require.main === module) {
    main();
}

// 导出函数供其他模块使用
module.exports = {
    generateOAuthLink,
    generateCodeChallenge,
    generateRandomString,
    base64URLEncode
};
