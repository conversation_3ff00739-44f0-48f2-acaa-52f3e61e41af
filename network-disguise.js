/**
 * 网络层伪装模块
 * 模拟真实的网络行为和请求模式
 */

class NetworkDisguise {
    constructor() {
        this.requestHistory = [];
        this.backgroundRequests = [];
    }

    /**
     * 设置网络请求拦截和伪装
     */
    async setupNetworkDisguise(page) {
        await page.setRequestInterception(true);

        page.on('request', async (request) => {
            const url = request.url();
            const resourceType = request.resourceType();
            
            // 记录请求历史
            this.requestHistory.push({
                url,
                resourceType,
                timestamp: Date.now(),
                method: request.method()
            });

            // 添加随机延迟模拟网络延迟
            if (Math.random() < 0.1) {
                await new Promise(resolve => setTimeout(resolve, Math.random() * 100));
            }

            // 修改请求头，添加更真实的头信息
            const headers = request.headers();
            
            // 添加一些常见的请求头
            if (!headers['sec-fetch-dest']) {
                headers['sec-fetch-dest'] = this.getSecFetchDest(resourceType);
            }
            
            if (!headers['sec-fetch-mode']) {
                headers['sec-fetch-mode'] = this.getSecFetchMode(resourceType);
            }
            
            if (!headers['sec-fetch-site']) {
                headers['sec-fetch-site'] = this.getSecFetchSite(url);
            }

            // 随机化请求头顺序（某些检测系统会检查这个）
            const randomizedHeaders = this.randomizeHeaderOrder(headers);

            try {
                await request.continue({ headers: randomizedHeaders });
            } catch (error) {
                // 如果请求已经被处理，忽略错误
                if (!error.message.includes('Request is already handled')) {
                    console.log('Request continue error:', error.message);
                }
            }
        });

        // 监听响应
        page.on('response', (response) => {
            const url = response.url();
            const status = response.status();
            
            // 记录响应信息
            const requestIndex = this.requestHistory.findIndex(req => req.url === url);
            if (requestIndex !== -1) {
                this.requestHistory[requestIndex].responseStatus = status;
                this.requestHistory[requestIndex].responseTime = Date.now();
            }
        });
    }

    /**
     * 获取Sec-Fetch-Dest值
     */
    getSecFetchDest(resourceType) {
        const mapping = {
            'document': 'document',
            'stylesheet': 'style',
            'script': 'script',
            'image': 'image',
            'font': 'font',
            'xhr': 'empty',
            'fetch': 'empty',
            'websocket': 'websocket'
        };
        return mapping[resourceType] || 'empty';
    }

    /**
     * 获取Sec-Fetch-Mode值
     */
    getSecFetchMode(resourceType) {
        const mapping = {
            'document': 'navigate',
            'stylesheet': 'no-cors',
            'script': 'no-cors',
            'image': 'no-cors',
            'font': 'cors',
            'xhr': 'cors',
            'fetch': 'cors'
        };
        return mapping[resourceType] || 'no-cors';
    }

    /**
     * 获取Sec-Fetch-Site值
     */
    getSecFetchSite(url) {
        try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname;
            
            if (hostname.includes('augmentcode.com') || hostname.includes('augment.com')) {
                return 'same-origin';
            } else if (hostname.includes('google.com') || hostname.includes('gstatic.com')) {
                return 'cross-site';
            } else {
                return 'cross-site';
            }
        } catch (error) {
            return 'cross-site';
        }
    }

    /**
     * 随机化请求头顺序
     */
    randomizeHeaderOrder(headers) {
        const entries = Object.entries(headers);
        
        // Fisher-Yates shuffle
        for (let i = entries.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [entries[i], entries[j]] = [entries[j], entries[i]];
        }
        
        return Object.fromEntries(entries);
    }

    /**
     * 生成背景网络活动
     */
    async generateBackgroundActivity(page) {
        const backgroundUrls = [
            'https://www.google.com/favicon.ico',
            'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap',
            'https://www.gstatic.com/hostedimg/382a91be1b2b8e9d_large',
            'https://ssl.gstatic.com/gb/images/bar/al-icon.png'
        ];

        // 随机选择一些URL进行后台请求
        const selectedUrls = backgroundUrls
            .sort(() => Math.random() - 0.5)
            .slice(0, Math.floor(Math.random() * 3) + 1);

        for (const url of selectedUrls) {
            try {
                // 随机延迟
                await new Promise(resolve => setTimeout(resolve, Math.random() * 2000));
                
                // 发起后台请求
                await page.evaluate((url) => {
                    const img = new Image();
                    img.src = url;
                }, url);
                
                this.backgroundRequests.push({
                    url,
                    timestamp: Date.now()
                });
            } catch (error) {
                // 忽略后台请求错误
            }
        }
    }

    /**
     * 模拟DNS预解析
     */
    async simulateDNSPrefetch(page) {
        const domains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'www.google.com',
            'ssl.gstatic.com'
        ];

        await page.evaluate((domains) => {
            domains.forEach(domain => {
                const link = document.createElement('link');
                link.rel = 'dns-prefetch';
                link.href = `//${domain}`;
                document.head.appendChild(link);
            });
        }, domains);
    }

    /**
     * 模拟资源预加载
     */
    async simulateResourcePreload(page) {
        await page.evaluate(() => {
            // 模拟预加载一些常见资源
            const preloadResources = [
                { href: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500&display=swap', as: 'style' },
                { href: 'https://www.google.com/favicon.ico', as: 'image' }
            ];

            preloadResources.forEach(resource => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.href = resource.href;
                link.as = resource.as;
                if (resource.as === 'style') {
                    link.onload = function() {
                        this.rel = 'stylesheet';
                    };
                }
                document.head.appendChild(link);
            });
        });
    }

    /**
     * 模拟Service Worker注册
     */
    async simulateServiceWorker(page) {
        await page.evaluate(() => {
            if ('serviceWorker' in navigator) {
                // 模拟Service Worker检查
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    // 只是检查，不实际注册
                });
            }
        });
    }

    /**
     * 模拟Web Push通知检查
     */
    async simulateNotificationCheck(page) {
        await page.evaluate(() => {
            if ('Notification' in window) {
                // 检查通知权限
                const permission = Notification.permission;
                
                if (permission === 'default') {
                    // 模拟检查但不实际请求权限
                    setTimeout(() => {
                        // 模拟用户交互后的权限检查
                    }, Math.random() * 5000);
                }
            }
        });
    }

    /**
     * 模拟WebRTC连接检查
     */
    async simulateWebRTCCheck(page) {
        await page.evaluate(() => {
            if (window.RTCPeerConnection) {
                try {
                    const pc = new RTCPeerConnection({
                        iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
                    });
                    
                    // 创建数据通道但不实际使用
                    const channel = pc.createDataChannel('test');
                    
                    // 短时间后关闭
                    setTimeout(() => {
                        pc.close();
                    }, 1000);
                } catch (error) {
                    // 忽略错误
                }
            }
        });
    }

    /**
     * 获取网络活动统计
     */
    getNetworkStats() {
        const now = Date.now();
        const recentRequests = this.requestHistory.filter(req => 
            now - req.timestamp < 60000 // 最近1分钟
        );

        return {
            totalRequests: this.requestHistory.length,
            recentRequests: recentRequests.length,
            backgroundRequests: this.backgroundRequests.length,
            averageResponseTime: this.calculateAverageResponseTime(),
            requestTypes: this.getRequestTypeDistribution()
        };
    }

    /**
     * 计算平均响应时间
     */
    calculateAverageResponseTime() {
        const completedRequests = this.requestHistory.filter(req => 
            req.responseTime && req.timestamp
        );

        if (completedRequests.length === 0) return 0;

        const totalTime = completedRequests.reduce((sum, req) => 
            sum + (req.responseTime - req.timestamp), 0
        );

        return Math.round(totalTime / completedRequests.length);
    }

    /**
     * 获取请求类型分布
     */
    getRequestTypeDistribution() {
        const distribution = {};
        
        this.requestHistory.forEach(req => {
            distribution[req.resourceType] = (distribution[req.resourceType] || 0) + 1;
        });

        return distribution;
    }

    /**
     * 清理历史记录
     */
    cleanup() {
        const cutoff = Date.now() - 300000; // 保留最近5分钟
        this.requestHistory = this.requestHistory.filter(req => req.timestamp > cutoff);
        this.backgroundRequests = this.backgroundRequests.filter(req => req.timestamp > cutoff);
    }
}

module.exports = NetworkDisguise;
