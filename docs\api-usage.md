# 🚀 Token API 使用指南

## 📋 概述

这是一个简单的 Token 管理 API，用于从 `tokens.json` 文件中获取未使用的 token。每次请求只返回一个 token，并自动标记为已使用。

## 🔧 配置和启动

### 1. 环境配置

在 `.env` 文件中配置认证密码：

```env
AUTH_PASSWORD=your_secret_password_here_change_this
TOKEN_API_PORT=9043
```

### 2. 启动服务器

有多种方式启动服务器：

```bash
# 方式 1: 直接启动
node token-api.js

# 方式 2: 使用 npm 脚本
npm run token-api

# 方式 3: 使用启动脚本（推荐）
npm run start-token-api
```

服务器将在端口 9043 上启动。

### 3. 测试 API

```bash
# 运行完整测试套件
npm run test-token-api
```

## 📡 API 端点

### 1. 健康检查

```http
GET /health
```

**响应示例：**

```json
{
  "success": true,
  "message": "Token API is running",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### 2. 获取未使用的 Token

```http
GET /api/tokens
Authorization: Bearer your_secret_password_here_change_this
Content-Type: application/json
User-Agent: VSCode-SimpleTokenManager/1.0.0
```

**成功响应：**

```json
{
  "success": true,
  "token": {
    "id": "9d4cada7-7dfe-4c4d-83a1-b8d35bf5cbdf",
    "accessToken": "c7dfeff57b2149bac7fd4c6d5c98b948136af6210201dc73e31909e5f740c534",
    "tenantURL": "https://d19.api.augmentcode.com/",
    "description": "Real token from Augment API via email verification",
    "createdAt": "2025-08-15T11:15:05.583Z"
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**无可用 Token 响应：**

```json
{
  "success": false,
  "error": "No available tokens",
  "message": "All tokens have been used or no tokens found",
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

**认证失败响应：**

```json
{
  "success": false,
  "error": "Unauthorized",
  "message": "Invalid authentication token"
}
```

### 3. 获取 Token 统计信息

```http
GET /api/tokens/stats
Authorization: Bearer your_secret_password_here_change_this
```

**响应示例：**

```json
{
  "success": true,
  "stats": {
    "total": 5,
    "used": 2,
    "unused": 3
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

## 🧪 测试示例

### 使用 curl 测试

```bash
# 健康检查
curl http://localhost:9043/health

# 获取 token
curl -X GET \
  -H "Authorization: Bearer your_secret_password_here_change_this" \
  -H "Content-Type: application/json" \
  -H "User-Agent: VSCode-SimpleTokenManager/1.0.0" \
  http://localhost:9043/api/tokens

# 获取统计信息
curl -X GET \
  -H "Authorization: Bearer your_secret_password_here_change_this" \
  http://localhost:9043/api/tokens/stats
```

### 使用 JavaScript 测试

```javascript
const axios = require("axios");

async function getToken() {
  try {
    const response = await axios.get("http://localhost:9043/api/tokens", {
      headers: {
        Authorization: "Bearer your_secret_password_here_change_this",
        "Content-Type": "application/json",
        "User-Agent": "VSCode-SimpleTokenManager/1.0.0",
      },
    });

    console.log("Token received:", response.data.token);
    return response.data.token;
  } catch (error) {
    console.error("Error:", error.response?.data || error.message);
  }
}

getToken();
```

## 📊 工作原理

1. **Token 存储**: 所有 token 存储在 `tokens.json` 文件中
2. **使用标记**: 返回的 token 会被标记 `used: true`
3. **单次返回**: 每次请求只返回一个未使用的 token
4. **自动过滤**: 自动过滤已使用的 token
5. **持久化**: 使用状态保存在文件中

## 🔒 安全注意事项

1. **修改默认密码**: 请务必修改 `.env` 文件中的 `AUTH_PASSWORD`
2. **HTTPS**: 生产环境建议使用 HTTPS
3. **防火墙**: 配置适当的防火墙规则
4. **日志监控**: 监控 API 访问日志

## 🐛 故障排除

### 常见问题

1. **401 Unauthorized**: 检查 Authorization header 和密码配置
2. **404 No available tokens**: 所有 token 都已被使用
3. **500 Internal server error**: 检查 tokens.json 文件格式和权限

### 日志查看

服务器会在控制台输出详细日志，包括：

- 服务器启动信息
- Token 使用情况
- 错误信息

### 重置 Token 使用状态

如需重置所有 token 为未使用状态，可以手动编辑 `tokens.json` 文件，删除所有 `"used": true` 字段。

## 📝 数据格式说明

### tokens.json 文件格式

```json
[
  {
    "id": "unique-token-id",
    "access_token": "actual-token-value",
    "tenant_url": "https://api.example.com/",
    "createdTime": "2025-08-15T11:15:05.583Z",
    "description": "Token description",
    "used": false // 此字段会自动添加
  }
]
```

### 字段映射

- `access_token` → `accessToken` (API 响应)
- `tenant_url` → `tenantURL` (API 响应)
- `createdTime` → `createdAt` (API 响应)
- `used` 字段用于标记是否已使用
