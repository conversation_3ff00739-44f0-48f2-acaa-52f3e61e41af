/**
 * 超级增强版抗指纹检测系统
 * 结合用户配置文件和智能行为模拟
 */

const UserProfileGenerator = require('./user-profile-generator.js');
const BehaviorSimulator = require('./behavior-simulator.js');

class EnhancedAntiFingerprint {
    constructor() {
        this.profileGenerator = new UserProfileGenerator();
        this.behaviorSimulator = new BehaviorSimulator();
        this.currentProfile = null;
    }

    /**
     * 获取超级增强的浏览器启动参数
     */
    getUltraLaunchOptions() {
        this.currentProfile = this.profileGenerator.getRandomProfile();
        
        return {
            headless: false, // 完全非headless模式
            args: [
                // 基础安全参数
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu-sandbox',
                '--disable-software-rasterizer',
                
                // 完全禁用自动化检测
                '--disable-blink-features=AutomationControlled',
                '--disable-features=VizDisplayCompositor',
                '--disable-ipc-flooding-protection',
                '--disable-renderer-backgrounding',
                '--disable-backgrounding-occluded-windows',
                '--disable-background-timer-throttling',
                
                // 网络和安全
                '--disable-web-security',
                '--disable-features=TranslateUI',
                '--disable-extensions',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-default-apps',
                '--disable-popup-blocking',
                '--disable-prompt-on-repost',
                '--disable-hang-monitor',
                '--disable-client-side-phishing-detection',
                '--disable-component-extensions-with-background-pages',
                '--disable-background-networking',
                '--aggressive-cache-discard',
                
                // 媒体和音频
                '--autoplay-policy=no-user-gesture-required',
                '--disable-background-media-suspend',
                '--disable-media-suspend',
                '--use-fake-ui-for-media-stream',
                '--use-fake-device-for-media-stream',
                
                // 根据配置文件设置窗口大小
                `--window-size=${this.currentProfile.viewport.width},${this.currentProfile.viewport.height}`,
                
                // 语言和地区
                '--lang=en-US,en',
                '--accept-lang=en-US,en;q=0.9',
                
                // 禁用各种检测
                '--disable-dev-tools',
                '--disable-plugins-discovery',
                '--disable-preconnect',
                '--disable-sync',
                '--metrics-recording-only',
                '--no-report-upload',
                '--disable-breakpad'
            ],
            defaultViewport: null, // 使用真实窗口大小
            timeout: 120000,
            ignoreDefaultArgs: [
                '--enable-automation',
                '--enable-blink-features=IdleDetection',
                '--disable-component-extensions-with-background-pages',
                '--disable-default-apps',
                '--disable-extensions'
            ],
            ignoreHTTPSErrors: true
        };
    }

    /**
     * 应用超级增强的抗指纹检测
     */
    async applyUltraAntiFingerprinting(page) {
        const profile = this.currentProfile;
        
        // 设置用户代理
        await page.setUserAgent(profile.userAgent);
        
        // 设置详细的HTTP头
        await page.setExtraHTTPHeaders({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': profile.languages.join(','),
            'Cache-Control': 'max-age=0',
            'Sec-Ch-Ua': this.generateSecChUa(profile),
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': `"${profile.os}"`,
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1',
            'Dnt': '1'
        });

        // 在每个新文档加载时执行超级抗指纹脚本
        await page.evaluateOnNewDocument((profileData) => {
            // 完全隐藏webdriver属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
                configurable: true
            });

            // 删除所有自动化相关属性
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Object;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_JSON;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Function;

            // 伪装Chrome对象
            if (!window.chrome) {
                window.chrome = {
                    runtime: {
                        onConnect: undefined,
                        onMessage: undefined
                    },
                    loadTimes: function() {
                        return {
                            commitLoadTime: Date.now() / 1000 - Math.random() * 10,
                            connectionInfo: 'http/1.1',
                            finishDocumentLoadTime: Date.now() / 1000 - Math.random() * 5,
                            finishLoadTime: Date.now() / 1000 - Math.random() * 3,
                            firstPaintAfterLoadTime: Date.now() / 1000 - Math.random() * 2,
                            firstPaintTime: Date.now() / 1000 - Math.random() * 8,
                            navigationType: 'Other',
                            npnNegotiatedProtocol: 'http/1.1',
                            requestTime: Date.now() / 1000 - Math.random() * 15,
                            startLoadTime: Date.now() / 1000 - Math.random() * 12,
                            wasAlternateProtocolAvailable: false,
                            wasFetchedViaSpdy: false,
                            wasNpnNegotiated: false
                        };
                    },
                    csi: function() {
                        return {
                            onloadT: Date.now(),
                            pageT: Date.now() - Math.random() * 1000,
                            startE: Date.now() - Math.random() * 2000,
                            tran: Math.floor(Math.random() * 20)
                        };
                    }
                };
            }

            // 伪装navigator属性
            Object.defineProperty(navigator, 'platform', {
                get: () => profileData.platform,
                configurable: true
            });

            Object.defineProperty(navigator, 'hardwareConcurrency', {
                get: () => profileData.hardwareConcurrency,
                configurable: true
            });

            Object.defineProperty(navigator, 'deviceMemory', {
                get: () => profileData.deviceMemory,
                configurable: true
            });

            Object.defineProperty(navigator, 'languages', {
                get: () => profileData.languages,
                configurable: true
            });

            // 伪装屏幕属性
            Object.defineProperty(screen, 'width', {
                get: () => profileData.screen.width,
                configurable: true
            });

            Object.defineProperty(screen, 'height', {
                get: () => profileData.screen.height,
                configurable: true
            });

            Object.defineProperty(screen, 'colorDepth', {
                get: () => profileData.screen.colorDepth,
                configurable: true
            });

            Object.defineProperty(screen, 'pixelDepth', {
                get: () => profileData.screen.colorDepth,
                configurable: true
            });

            // 伪装时区
            const originalGetTimezoneOffset = Date.prototype.getTimezoneOffset;
            Date.prototype.getTimezoneOffset = function() {
                return -300; // EST timezone
            };

            // 伪装WebGL指纹
            const getParameter = WebGLRenderingContext.prototype.getParameter;
            WebGLRenderingContext.prototype.getParameter = function(parameter) {
                if (parameter === 37445) {
                    return profileData.webgl.vendor;
                }
                if (parameter === 37446) {
                    return profileData.webgl.renderer;
                }
                return getParameter.call(this, parameter);
            };

            // 同样处理WebGL2
            if (window.WebGL2RenderingContext) {
                const getParameter2 = WebGL2RenderingContext.prototype.getParameter;
                WebGL2RenderingContext.prototype.getParameter = function(parameter) {
                    if (parameter === 37445) {
                        return profileData.webgl.vendor;
                    }
                    if (parameter === 37446) {
                        return profileData.webgl.renderer;
                    }
                    return getParameter2.call(this, parameter);
                };
            }

            // 伪装Canvas指纹
            const originalToDataURL = HTMLCanvasElement.prototype.toDataURL;
            HTMLCanvasElement.prototype.toDataURL = function() {
                const context = this.getContext('2d');
                if (context) {
                    // 添加微小的随机噪声
                    const imageData = context.getImageData(0, 0, this.width, this.height);
                    for (let i = 0; i < imageData.data.length; i += 4) {
                        if (Math.random() < 0.001) {
                            imageData.data[i] = Math.min(255, imageData.data[i] + Math.floor(Math.random() * 3) - 1);
                            imageData.data[i + 1] = Math.min(255, imageData.data[i + 1] + Math.floor(Math.random() * 3) - 1);
                            imageData.data[i + 2] = Math.min(255, imageData.data[i + 2] + Math.floor(Math.random() * 3) - 1);
                        }
                    }
                    context.putImageData(imageData, 0, 0);
                }
                return originalToDataURL.apply(this, arguments);
            };

            // 伪装AudioContext指纹
            if (window.AudioContext || window.webkitAudioContext) {
                const OriginalAudioContext = window.AudioContext || window.webkitAudioContext;
                const audioContextProxy = function() {
                    const context = new OriginalAudioContext();
                    
                    // 伪装音频属性
                    Object.defineProperty(context, 'sampleRate', {
                        get: () => profileData.audio.sampleRate,
                        configurable: true
                    });
                    
                    return context;
                };
                
                if (window.AudioContext) {
                    window.AudioContext = audioContextProxy;
                }
                if (window.webkitAudioContext) {
                    window.webkitAudioContext = audioContextProxy;
                }
            }

        }, profile);

        // 设置视口
        await page.setViewport({
            width: profile.viewport.width,
            height: profile.viewport.height,
            deviceScaleFactor: 1,
            isMobile: false,
            hasTouch: false,
            isLandscape: profile.viewport.width > profile.viewport.height
        });

        console.log(`🎭 已应用用户配置文件: ${profile.os} ${profile.userAgent.split(' ')[0]}`);
    }

    /**
     * 生成Sec-Ch-Ua头
     */
    generateSecChUa(profile) {
        const version = profile.userAgent.match(/Chrome\/(\d+)/)?.[1] || '120';
        return `"Not_A Brand";v="8", "Chromium";v="${version}", "Google Chrome";v="${version}"`;
    }

    /**
     * 智能等待（结合行为模拟）
     */
    async smartWait(page, baseTime = 1000) {
        await this.behaviorSimulator.simulateThinking(baseTime);
        await this.behaviorSimulator.randomPageInteraction(page);
    }

    /**
     * 智能输入（使用行为模拟器）
     */
    async smartType(page, selector, text) {
        return await this.behaviorSimulator.smartType(page, selector, text);
    }

    /**
     * 智能点击（使用行为模拟器）
     */
    async smartClick(page, selector) {
        return await this.behaviorSimulator.smartClick(page, selector);
    }

    /**
     * 模拟阅读行为
     */
    async simulateReading(page, duration = 2000) {
        return await this.behaviorSimulator.simulateReading(page, duration);
    }

    /**
     * 模拟犹豫行为
     */
    async simulateHesitation(page) {
        return await this.behaviorSimulator.simulateHesitation(page);
    }

    /**
     * 获取当前配置文件信息
     */
    getCurrentProfile() {
        return this.currentProfile;
    }

    /**
     * 获取行为统计
     */
    getBehaviorStats() {
        return this.behaviorSimulator.getBehaviorStats();
    }
}

module.exports = EnhancedAntiFingerprint;
