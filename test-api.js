const axios = require('axios');

async function testAPI() {
  const baseURL = 'http://localhost:9043';
  const authHeader = {
    'Authorization': 'Bearer your_secret_password_here_change_this',
    'Content-Type': 'application/json',
    'User-Agent': 'VSCode-SimpleTokenManager/1.0.0'
  };

  console.log('🧪 Testing Token API...\n');
  console.log('📍 Server URL:', baseURL);
  console.log('🔐 Using authentication password from .env file\n');

  let testsPassed = 0;
  let totalTests = 0;

  // Helper function to run a test
  async function runTest(testName, testFunction) {
    totalTests++;
    try {
      console.log(`${totalTests}. ${testName}...`);
      await testFunction();
      testsPassed++;
      console.log(`✅ ${testName} passed\n`);
    } catch (error) {
      console.log(`❌ ${testName} failed:`, error.message);
      if (error.response?.data) {
        console.log('   Response:', error.response.data);
      }
      console.log('');
    }
  }

  // Test 1: Health check
  await runTest('Health check', async () => {
    const response = await axios.get(`${baseURL}/health`);
    if (!response.data.success) {
      throw new Error('Health check returned success: false');
    }
    console.log('   Response:', response.data);
  });

  // Test 2: Get token stats
  await runTest('Token statistics', async () => {
    const response = await axios.get(`${baseURL}/api/tokens/stats`, { headers: authHeader });
    if (!response.data.success) {
      throw new Error('Stats request returned success: false');
    }
    console.log('   Stats:', response.data.stats);
  });

  // Test 3: Try to get a token (might fail if no tokens available)
  await runTest('Get token', async () => {
    try {
      const response = await axios.get(`${baseURL}/api/tokens`, { headers: authHeader });
      if (!response.data.success) {
        throw new Error('Token request returned success: false');
      }
      console.log('   Token ID:', response.data.token.id);
      console.log('   Tenant URL:', response.data.token.tenantURL);
    } catch (error) {
      if (error.response?.status === 404 && error.response?.data?.error === 'No available tokens') {
        console.log('   No tokens available (this is expected if all tokens are used)');
        return; // This is not an error
      }
      throw error;
    }
  });

  // Test 4: Test authentication failure
  await runTest('Authentication failure', async () => {
    try {
      const response = await axios.get(`${baseURL}/api/tokens`, {
        headers: { 'Authorization': 'Bearer wrong_password' }
      });
      throw new Error('Should have failed authentication but succeeded');
    } catch (error) {
      if (error.response?.status !== 401) {
        throw new Error(`Expected 401 status but got ${error.response?.status}`);
      }
      console.log('   Correctly rejected with 401 Unauthorized');
    }
  });

  // Test 5: Test missing authentication
  await runTest('Missing authentication', async () => {
    try {
      const response = await axios.get(`${baseURL}/api/tokens`);
      throw new Error('Should have failed authentication but succeeded');
    } catch (error) {
      if (error.response?.status !== 401) {
        throw new Error(`Expected 401 status but got ${error.response?.status}`);
      }
      console.log('   Correctly rejected with 401 Unauthorized');
    }
  });

  // Test 6: Test invalid endpoint
  await runTest('Invalid endpoint', async () => {
    try {
      const response = await axios.get(`${baseURL}/api/invalid`, { headers: authHeader });
      throw new Error('Should have returned 404 but succeeded');
    } catch (error) {
      if (error.response?.status !== 404) {
        throw new Error(`Expected 404 status but got ${error.response?.status}`);
      }
      console.log('   Correctly returned 404 Not Found');
    }
  });

  // Summary
  console.log('📊 Test Summary:');
  console.log(`   Passed: ${testsPassed}/${totalTests}`);
  console.log(`   Success Rate: ${Math.round((testsPassed / totalTests) * 100)}%`);

  if (testsPassed === totalTests) {
    console.log('🎉 All tests passed! API is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the API configuration.');
    process.exit(1);
  }
}

// Check if server is running
async function checkServerRunning() {
  try {
    await axios.get('http://localhost:9043/health', { timeout: 2000 });
    return true;
  } catch (error) {
    return false;
  }
}

async function main() {
  console.log('🔍 Checking if Token API server is running...');

  const isRunning = await checkServerRunning();
  if (!isRunning) {
    console.log('❌ Token API server is not running on port 9043');
    console.log('💡 Please start the server first:');
    console.log('   npm run token-api');
    console.log('   or');
    console.log('   node token-api.js');
    process.exit(1);
  }

  console.log('✅ Server is running, starting tests...\n');
  await testAPI();
}

main().catch(console.error);
